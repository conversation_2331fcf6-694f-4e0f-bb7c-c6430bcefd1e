#!/usr/bin/env python3
"""
Простой тест API без внешних зависимостей
"""

import urllib.request
import urllib.parse
import json
import os


def test_health():
    """Тест health endpoint"""
    try:
        with urllib.request.urlopen("http://localhost:8000/health") as response:
            data = json.loads(response.read().decode())
            print(f"Health check: {data}")
            return True
    except Exception as e:
        print(f"Health check failed: {e}")
        return False


def test_conversion():
    """Тест конвертации"""
    audio_file = "1093.mp3"

    if not os.path.exists(audio_file):
        print(f"File {audio_file} not found")
        return False

    print(f"Testing conversion with {audio_file}")
    print(f"File size: {os.path.getsize(audio_file)} bytes")

    # Для простоты используем curl через os.system
    cmd = f"""curl -X POST "http://localhost:8000/convert" \
        -F "audio_file=@{audio_file}" \
        -F "f0_up_key=0" \
        -F "index_rate=0.75" \
        -F "filter_radius=3" \
        -F "rms_mix_rate=0.25" \
        -F "protect=0.33" \
        -F "f0_method=rmvpe" \
        -o converted_output.wav"""

    print("Running conversion...")
    result = os.system(cmd)

    if result == 0 and os.path.exists("converted_output.wav"):
        print("✅ Conversion successful!")
        print("Output file: converted_output.wav")
        print(f"Output size: {os.path.getsize('converted_output.wav')} bytes")
        return True
    else:
        print(f"❌ Conversion failed (exit code: {result})")
        return False


def main():
    print("=== Simple RVC API Test ===")

    print("\n1. Testing health endpoint...")
    if not test_health():
        print("Server not available")
        return False

    print("\n2. Testing voice conversion...")
    success = test_conversion()

    if success:
        print("\n🎉 Test completed successfully!")
    else:
        print("\n❌ Test failed")

    return success


if __name__ == "__main__":
    main()
