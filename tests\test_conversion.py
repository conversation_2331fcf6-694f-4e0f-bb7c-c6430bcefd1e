#!/usr/bin/env python3
"""
Тест конвертации голоса с файлом 1093.mp3
"""

import requests
import os
import sys


def test_conversion():
    """Тест конвертации с реальным аудио файлом"""

    # Проверяем наличие тестового файла
    audio_file = "1093.mp3"
    if not os.path.exists(audio_file):
        print(f"Тестовый файл {audio_file} не найден!")
        return False

    print(f"Тестируем конвертацию с файлом: {audio_file}")
    print(f"Размер файла: {os.path.getsize(audio_file)} байт")

    try:
        # Отправляем запрос на конвертацию
        with open(audio_file, "rb") as f:
            files = {"audio_file": (audio_file, f, "audio/mpeg")}
            data = {
                "f0_up_key": 0,  # Без изменения тональности
                "index_rate": 0.75,  # Стандартное значение
                "filter_radius": 3,  # Стандартное значение
                "rms_mix_rate": 0.25,  # Стандартное значение
                "protect": 0.33,  # Стандартное значение
                "f0_method": "rmvpe",  # Лучший метод
            }

            print("Отправляем запрос на конвертацию...")
            print(f"Параметры: {data}")

            response = requests.post(
                "http://localhost:8000/convert",
                files=files,
                data=data,
                timeout=120,  # 2 минуты таймаут
            )

        print(f"Статус ответа: {response.status_code}")

        if response.status_code == 200:
            # Сохраняем результат
            output_file = "converted_1093.wav"
            with open(output_file, "wb") as f:
                f.write(response.content)

            print("✅ Конвертация успешна!")
            print(f"Результат сохранен в: {output_file}")
            print(f"Размер результата: {os.path.getsize(output_file)} байт")

            # Проверяем заголовки
            if "X-Processing-Time" in response.headers:
                print(
                    f"Время обработки: {response.headers['X-Processing-Time']} секунд"
                )

            return True
        else:
            print(f"❌ Ошибка конвертации: {response.status_code}")
            print(f"Ответ сервера: {response.text}")
            return False

    except requests.exceptions.Timeout:
        print("❌ Таймаут запроса (превышено время ожидания)")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Ошибка подключения к серверу")
        return False
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        return False


def test_health():
    """Проверка работоспособности сервера"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Сервер работает: {data}")
            return True
        else:
            print(f"❌ Проблема с сервером: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Сервер недоступен: {e}")
        return False


def main():
    """Основная функция"""
    print("=== Тест конвертации голоса RVC ===\n")

    # Проверяем сервер
    print("1. Проверка сервера...")
    if not test_health():
        print(
            "Сервер недоступен. Убедитесь, что сервер запущен командой: uv run main.py"
        )
        return False
    print()

    # Тестируем конвертацию
    print("2. Тестирование конвертации...")
    success = test_conversion()
    print()

    if success:
        print("🎉 Все тесты прошли успешно!")
        print("\nТеперь вы можете:")
        print("- Открыть http://localhost:8000/docs для интерактивной документации API")
        print("- Использовать converted_1093.wav для проверки качества конвертации")
    else:
        print("❌ Тесты не прошли")

    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
