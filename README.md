# RVC FastAPI Voice Conversion Service

FastAPI сервис для конвертации голоса с использованием RVC (Retrieval-based Voice Conversion) и RMVPE для извлечения f0.

## Особенности

- 🎤 Конвертация голоса в реальном времени через REST API
- 🔊 Поддержка RMVPE для высококачественного извлечения f0
- ⚡ Асинхронная обработка запросов
- 🛠️ Настраиваемые параметры конвертации
- 📊 Автоматическая оптимизация под доступное оборудование
- 🐳 Готов к контейнеризации

## Требования

- Python 3.8+
- CUDA (опционально, для GPU ускорения)
- FFmpeg

## Установка

1. Перейдите в директорию fastapi:
```bash
cd fastapi
```

2. Установите зависимости:
```bash
uv sync
```

Или с поддержкой GPU:
```bash
uv sync
uv add faiss-gpu
```

3. Убедитесь, что в корневой директории проекта есть необходимые файлы:
   - `weights/w_zaporozhec1_ru.pth` - обученная модель голоса
   - `hubert_base.pt` - Hubert модель
   - `rmvpe.pt` - RMVPE модель

## Запуск

### Разработка
```bash
un run main.py
```

### Продакшн
```bash
uv run uvicorn main:app --host 0.0.0.0 --port 8000
```

## API Документация

После запуска сервиса документация доступна по адресу:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

### Основные эндпойнты

#### POST /convert
Конвертация голоса

**Параметры:**
- `audio_file` (file): Входной аудио файл (WAV, MP3, FLAC)
- `f0_up_key` (int, default=0): Изменение тональности в полутонах (-12 до +12)
- `index_rate` (float, default=0.75): Коэффициент использования индекса (0.0-1.0)
- `filter_radius` (int, default=3): Радиус медианного фильтра (0-7)
- `rms_mix_rate` (float, default=0.25): Коэффициент смешивания RMS (0.0-1.0)
- `protect` (float, default=0.33): Защита согласных звуков (0.0-0.5)

**Ответ:** Конвертированный аудио файл

#### GET /health
Проверка состояния сервиса

#### GET /
Информация о сервисе

## Примеры использования

### cURL
```bash
curl -X POST "http://localhost:8000/convert" \
  -H "accept: audio/wav" \
  -H "Content-Type: multipart/form-data" \
  -F "audio_file=@input.wav" \
  -F "f0_up_key=0" \
  -F "index_rate=0.75" \
  --output converted.wav
```

### Python
```python
import requests

url = "http://localhost:8000/convert"
files = {"audio_file": open("input.wav", "rb")}
data = {
    "f0_up_key": 0,
    "index_rate": 0.75,
    "filter_radius": 3,
    "rms_mix_rate": 0.25,
    "protect": 0.33
}

response = requests.post(url, files=files, data=data)

if response.status_code == 200:
    with open("converted.wav", "wb") as f:
        f.write(response.content)
```

## Конфигурация

Настройки можно изменить через переменные окружения с префиксом `RVC_`:

```bash
export RVC_DEVICE="cuda:0"
export RVC_IS_HALF=true
export RVC_MODEL_PATH="/path/to/model.pth"
```

## Производительность

- **CPU**: Работает на любом современном CPU, время обработки ~10-30 секунд на минуту аудио
- **GPU**: Значительно быстрее, время обработки ~1-5 секунд на минуту аудио
- **Память**: Требует 2-8GB RAM в зависимости от модели и настроек

## Ограничения

- Максимальный размер файла: 100MB
- Поддерживаемые форматы: WAV, MP3, FLAC, OGG
- Рекомендуемая длительность аудио: до 10 минут

## Устранение неполадок

### Ошибка "Model file not found"
Убедитесь, что файлы моделей находятся в правильных директориях относительно корня проекта.

### Ошибка CUDA out of memory
Уменьшите `is_half` до `false` или используйте CPU режим.

### Медленная обработка
Убедитесь, что используется GPU и правильно настроены CUDA драйверы.