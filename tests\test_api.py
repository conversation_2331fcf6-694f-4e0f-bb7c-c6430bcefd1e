#!/usr/bin/env python3
"""
Тестовый скрипт для проверки работы RVC FastAPI сервиса
"""

import requests
import sys
import os
import time


def test_health_check(base_url: str = "http://localhost:8000"):
    """Тест проверки здоровья сервиса"""
    print("🔍 Testing health check...")

    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Health check error: {e}")
        return False


def test_root_endpoint(base_url: str = "http://localhost:8000"):
    """Тест корневого эндпойнта"""
    print("🔍 Testing root endpoint...")

    try:
        response = requests.get(f"{base_url}/", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Root endpoint passed: {data}")
            return True
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Root endpoint error: {e}")
        return False


def test_voice_conversion(
    audio_file_path: str,
    base_url: str = "http://localhost:8000",
    output_path: str = "test_output.wav",
):
    """Тест конвертации голоса"""
    print(f"🔍 Testing voice conversion with file: {audio_file_path}")

    if not os.path.exists(audio_file_path):
        print(f"❌ Audio file not found: {audio_file_path}")
        return False

    try:
        # Подготовка файла
        with open(audio_file_path, "rb") as f:
            files = {"audio_file": f}
            data = {
                "f0_up_key": 0,
                "index_rate": 0.75,
                "filter_radius": 3,
                "rms_mix_rate": 0.25,
                "protect": 0.33,
            }

            print("📤 Sending conversion request...")
            start_time = time.time()

            response = requests.post(
                f"{base_url}/convert",
                files=files,
                data=data,
                timeout=300,  # 5 минут таймаут
            )

            processing_time = time.time() - start_time

            if response.status_code == 200:
                # Сохранение результата
                with open(output_path, "wb") as output_file:
                    output_file.write(response.content)

                file_size = len(response.content)
                print("✅ Voice conversion successful!")
                print(f"   Processing time: {processing_time:.2f} seconds")
                print(f"   Output file size: {file_size} bytes")
                print(f"   Output saved to: {output_path}")

                # Проверка заголовков ответа
                if "X-Processing-Time" in response.headers:
                    server_time = response.headers["X-Processing-Time"]
                    print(f"   Server processing time: {server_time} seconds")

                return True
            else:
                print(f"❌ Voice conversion failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False

    except requests.exceptions.Timeout:
        print("❌ Voice conversion timeout (>5 minutes)")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Voice conversion error: {e}")
        return False


def test_invalid_file(base_url: str = "http://localhost:8000"):
    """Тест с невалидным файлом"""
    print("🔍 Testing with invalid file...")

    try:
        # Создание фейкового текстового файла
        fake_content = b"This is not an audio file"
        files = {"audio_file": ("fake.txt", fake_content, "text/plain")}
        data = {"f0_up_key": 0}

        response = requests.post(
            f"{base_url}/convert", files=files, data=data, timeout=30
        )

        if response.status_code == 400:
            print("✅ Invalid file correctly rejected")
            return True
        else:
            print(
                f"❌ Invalid file test failed: expected 400, got {response.status_code}"
            )
            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ Invalid file test error: {e}")
        return False


def main():
    """Основная функция тестирования"""
    print("🚀 Starting RVC FastAPI tests...\n")

    base_url = "http://localhost:8000"

    # Проверка аргументов командной строки
    if len(sys.argv) > 1:
        base_url = sys.argv[1]

    print(f"Testing API at: {base_url}\n")

    # Список тестов
    tests = [
        ("Health Check", lambda: test_health_check(base_url)),
        ("Root Endpoint", lambda: test_root_endpoint(base_url)),
        ("Invalid File", lambda: test_invalid_file(base_url)),
    ]

    # Поиск тестового аудио файла
    test_audio_paths = [
        "test_audio.wav",
        "../opt/amy_11_044qefftp9.wav.reformatted.wav_main_vocal.flac",
        "sample.wav",
        "test.wav",
    ]

    test_audio_file = None
    for path in test_audio_paths:
        if os.path.exists(path):
            test_audio_file = path
            break

    if test_audio_file:
        tests.append(
            (
                "Voice Conversion",
                lambda: test_voice_conversion(test_audio_file, base_url),
            )
        )
    else:
        print("⚠️  No test audio file found. Skipping voice conversion test.")
        print(f"   Searched for: {test_audio_paths}")

    # Выполнение тестов
    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n{'=' * 50}")
        print(f"Test: {test_name}")
        print("=" * 50)

        if test_func():
            passed += 1

        time.sleep(1)  # Небольшая пауза между тестами

    # Результаты
    print(f"\n{'=' * 50}")
    print("TEST RESULTS")
    print("=" * 50)
    print(f"Passed: {passed}/{total}")

    if passed == total:
        print("🎉 All tests passed!")
        sys.exit(0)
    else:
        print("❌ Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
