{"很遗憾您这没有能用的显卡来支持您训练": "К сожалению у вас нету видеокарты, которая поддерживает тренировку модели.", "是": "Да", "step1:正在处理数据": "Шаг 1: Переработка данных", "step2a:无需提取音高": "Шаг 2а: Пропуск вытаскивания тональности", "step2b:正在提取特征": "Шаг 2б: Вытаскивание черт", "step3a:正在训练模型": "Шаг 3а: Тренировка модели начата", "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log": "Тренировка завершена. Вы можете проверить логи тренировки в консоли или в файле 'train.log' в папке модели.", "全流程结束！": "Все процессы завершены!", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.", "模型推理": "Обработка модели", "推理音色": "Обработка голоса:", "刷新音色列表和索引路径": "Обновить список голосов и индексов", "卸载音色省显存": "Выгрузить голос для сохранения памяти видеокарты:", "请选择说话人id": "Выбери айди голоса:", "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ": "Рекомендованно +12 для конвертирования мужского голоса в женский и -12 для конвертирования женского в мужской. Если диапазон голоса слищком велик и голос искажается, значение можно изменить на свой вкус.", "变调(整数, 半音数量, 升八度12降八度-12)": "Высота голоса (число, полутоны, поднять на октаву: 12, понизить на октаву: -12):", "输入待处理音频文件路径(默认是正确格式示例)": "Введите путь к аудиофайлу, который хотите переработать (по умолчанию введён правильный формат):", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU": "Выберите алгоритм вытаскивания тональности ('pm': быстрое извлечение но качество речи хуже; 'harvest': бассы лучше но очень медленный; 'crepe': лучшее качество но сильно использует видеокарту):", ">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音": "Если больше 3: применить медианную фильтрацию к вытащенным тональностям. Значение контролирует радиус фильтра и может уменьшить излишнее дыхание.", "特征检索库文件路径,为空则使用下拉的选择结果": "Путь к файлу индекса черт. Оставьте пустым, чтобы использовать выбранный результат из списка:", "自动检测index路径,下拉式选择(dropdown)": "Автоматически найти путь к индексу и выбрать его из списка:", "特征文件路径": "Путь к файлу черт:", "检索特征占比": "Соотношение поиска черт:", "后处理重采样至最终采样率，0为不进行重采样": "Изменить частоту дискретизации в выходном файле на финальную. Поставьте 0, чтобы ничего не изменялось:", "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络": "Использовать громкость входного файла для замены или перемешивания с громкостью выходного файла. Чем ближе соотношение к 1, тем больше используется звука из выходного файла:", "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果": "Защитить глухие согласные и звуки дыхания для предотвращения артефактов, например разрывание в электронной музыке. Поставьте на 0.5, чтобы выключить. Уменьшите значение для повышения защиты, но при этом может ухудшиться аккуратность индексирования:", "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调": "Файл дуги F0 (не обязательно). Одна тональность на каждую строчку. Заменяет обычный F0 и модуляцию тональности:", "转换": "Конвертировать", "输出信息": "Выходная информация", "输出音频(右下角三个点,点了可以下载)": "Экспортировать аудиофайл (нажми на три точки в правом нижнем углу для загрузки)", "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ": "Конвертировать пачкой. Введите путь к папке, в которой находятся файлы для конвертирования или выложите несколько аудиофайлов. Сконвертированные файлы будут сохранены в указанной папке (по умолчанию 'opt').", "指定输出文件夹": "Укажите выходную папку:", "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)": "Введите путь к папке с аудио для переработки:", "也可批量输入音频文件, 二选一, 优先读文件夹": "Вы также можете выложить аудиофайлы пачкой. Выберите одно из двух. Приоритет отдаётся считыванию из папки.", "导出文件格式": "Формат выходного файла", "伴奏人声分离&去混响&去回声": "Отделение вокала/инструментала и убирание эхо", "人声伴奏分离批量处理， 使用UVR5模型。 <br>合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。 <br>模型分为三类： <br>1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点； <br>2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型； <br> 3、去混响、去延迟模型（by FoxJoy）：<br>  (1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；<br>&emsp;(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。<br>去混响/去延迟，附：<br>1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；<br>2、MDX-Net-Dereverb模型挺慢的；<br>3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "Пакетная обработка для разделения вокального сопровождения с использованием модели UVR5.<br>Пример допустимого формата пути к папке: D:\\path\\to\\input\\folder<br> Модель разделена на три категории:<br>1. Сохранить вокал: выберите этот вариант для звука без гармоний. Он сохраняет вокал лучше, чем HP5. Он включает в себя две встроенные модели: HP2 и HP3. HP3 может немного пропускать инструментал, но сохраняет вокал немного лучше, чем HP2.<br>2. Сохранить только основной вокал: выберите этот вариант для звука с гармониями. Это может ослабить основной вокал. Он включает одну встроенную модель: HP5.<br>3. Модели удаления реверберации и задержки (от FoxJoy):<br>  (1) MDX-Net: лучший выбор для удаления стереореверберации, но он не может удалить монореверберацию;<br>&emsp;(234) DeEcho: удаляет эффекты задержки. Агрессивный режим удаляет более тщательно, чем Нормальный режим. DeReverb дополнительно удаляет реверберацию и может удалять монореверберацию, но не очень эффективно для сильно реверберированного высокочастотного контента.<br>Примечания по удалению реверберации/задержки:<br>1. Время обработки для модели DeEcho-DeReverb примерно в два раза больше, чем для двух других моделей DeEcho.<br>2. Модель MDX-Net-Dereverb довольно медленная.<br>3. Рекомендуемая самая чистая конфигурация — сначала применить MDX-Net, а затем DeEcho-Aggressive.", "输入待处理音频文件夹路径": "Введите путь к папке с аудиофайлами для переработки:", "模型": "Модели", "指定输出主人声文件夹": "Введите путь к папке для вокала:", "指定输出非主人声文件夹": "Введите путь к папке для инструментала:", "训练": "Тренировка", "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ": "Шаг 1: Заполните настройки модели. Данные модели сохранены в папку 'logs' и для каждой модели создаётся отдельная папка. Введите вручную путь к настройкам для модели, в которой находятся логи и тренировочные файлы.", "输入实验名": "Введите название модели:", "目标采样率": "Частота дискретизации модели:", "模型是否带音高指导(唱歌一定要, 语音可以不要)": "Наведение по тональности у модели (обязательно для пения, необязательно для речи):", "版本": "Версия", "提取音高和处理数据使用的CPU进程数": "Число процессов ЦП, используемое для вытаскивания тональностей и обрабротки данных:", "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ": "Шаг 2а: Автоматически пройтись по всем аудиофайлам в папке тренировки и нормализировать куски. Создаст 2 папки wav в папке модели. В данных момент поддерживается тренировка только одного голоса.", "输入训练文件夹路径": "Введите путь к папке тренировки:", "请指定说话人id": "Введите айди голоса:", "处理数据": "Переработать данные", "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)": "Шаг 2б: Вытащить тональности с помошью процессора (если в модели есть тональности), вытащить черты с помощью видеокарты (выберите какой):", "以-分隔输入使用的卡号, 例如   0-1-2   使用卡0和卡1和卡2": "Введите, какие(-ую) видеокарты(-у) хотите использовать через '-', например 0-1-2, чтобы использовать видеокарту 0, 1 и 2:", "显卡信息": "Информация о видеокартах", "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢": "Выберите алгоритм вытаскивания тональности ('pm': быстрое извлечение но качество речи хуже; 'harvest': бассы лучше но очень медленный; 'crepe': лучшее качество но сильно использует видеокарту):", "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程": "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程", "特征提取": "Вытаскивание черт", "step3: 填写训练设置, 开始训练模型和索引": "Шаг 3: Заполните остальные настройки тренировки и начните тренировать модель и индекс", "保存频率save_every_epoch": "Частота сохранения (save_every_epoch):", "总训练轮数total_epoch": "Полное количество эпох (total_epoch):", "每张显卡的batch_size": "Размер пачки для видеокарты:", "是否仅保存最新的ckpt文件以节省硬盘空间": "Сохранять только последний файл '.ckpt', чтобы сохранить место на диске:", "否": "Нет", "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速": "Кэшировать все тренировочные сеты в видеопамять. Кэширование маленький датасетов (меньше 10 минут) может ускорить тренировку, но кэширование больших, наоборот, займёт много видеопамяти и не сильно ускорит тренировку:", "是否在每次保存时间点将最终小模型保存至weights文件夹": "Сохранять маленькую финальную модель в папку 'weights' на каждой точке сохранения:", "加载预训练底模G路径": "Путь к натренированой базовой модели G:", "加载预训练底模D路径": "Путь к натренированой базовой модели D:", "训练模型": "Тренировать модель", "训练特征索引": "Тренировать индекс черт", "一键训练": "Тренировка одним нажатием", "ckpt处理": "Обработка ckpt", "模型融合, 可用于测试音色融合": "Слияние моделей, может быть использовано для проверки слияния тембра", "A模型路径": "Путь к модели А:", "B模型路径": "Путь к модели Б:", "A模型权重": "Вес (w) модели А::", "模型是否带音高指导": "Есть ли у модели наведение по тональности (1: да, 0: нет):", "要置入的模型信息": "Информация о модели:", "保存的模型名不带后缀": "Название сохранённой модели (без расширения):", "模型版本型号": "Версия архитектуры модели:", "融合": "Слияние", "修改模型信息(仅支持weights文件夹下提取的小模型文件)": "Модифицировать информацию о модели (поддерживается только для маленких моделей, взятых из папки 'weights')", "模型路径": "Путь к папке:", "要改的模型信息": "Информация о модели, которую нужно модифицировать:", "保存的文件名, 默认空为和源文件同名": "Название сохранённого файла (по умолчанию такое же, как и входного):", "修改": "Модифици<PERSON>овать", "查看模型信息(仅支持weights文件夹下提取的小模型文件)": "Просмотреть информацию о модели (поддерживается только для маленких моделей, взятых из папки 'weights')", "查看": "Просмотр", "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况": "Вытаскивание модели (введите путь к большому файлу модели в папке 'logs'). Полезно, если Вам нужно заверщить тренировку и вручную достать и сохранить маленький файл модели, или если Вам нужно проверить незаконченную модель:", "保存名": "Имя сохранённого файла:", "模型是否带音高指导,1是0否": "Есть ли у модели наведение по тональности (1: да, 0: нет):", "提取": "Вытащить", "Onnx导出": "Экспортировать Onnx", "RVC模型路径": "Путь к модели RVC:", "Onnx输出路径": "Путь для экспотрированного Onnx:", "导出Onnx模型": "Экспортировать Onnx модель", "常见问题解答": "ЧаВО (Часто задаваемые вопросы)", "招募音高曲线前端编辑器": "Использование фронтенд редакторов для тональных дуг", "加开发群联系我xxxxx": "Присоединитесь к группе разработки и свяжитесь со мной по xxxxx", "点击查看交流、问题反馈群号": "Нажмите, чтобы просмотреть номер группы коммуникации и отзывах о проблемах", "xxxxx": "xxxxx", "加载模型": "Загрузить модель", "Hubert模型": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Hubert", "选择.pth文件": "Выбрать файл .pth", "选择.index文件": "Выбрать файл .index", "选择.npy文件": "Выбрать файл .npy", "输入设备": "Входное устройство", "输出设备": "Выходное устройство", "音频设备(请使用同种类驱动)": "Аудио устройство (пожалуйста используйте такой=же тип драйвера)", "响应阈值": "Порог ответа", "音调设置": "Настройки тональности", "Index Rate": "Темп индекса", "常规设置": "Основные настройки", "采样长度": "<PERSON><PERSON><PERSON>на сэмпла", "淡入淡出长度": "<PERSON><PERSON><PERSON><PERSON> затухания", "额外推理时长": "Доп. время переработки", "输入降噪": "Уменьшения шума во входной информации", "输出降噪": "Уменьшения шума во выходной информации", "性能设置": "Настройки быстроты", "开始音频转换": "Начать конвертацию аудио", "停止音频转换": "Закончить конвертацию аудио", "推理时间(ms):": "Время переработки (мс):", "请选择pth文件": "请选择pth文件", "请选择index文件": "请选择index文件", "hubert模型路径不可包含中文": "hubert模型路径不可包含中文", "pth文件路径不可包含中文": "pth文件路径不可包含中文", "index文件路径不可包含中文": "index文件路径不可包含中文", "重载设备列表": "Перезагрузить список устройств", "音高算法": "音高算法", "harvest进程数": "harvest进程数"}