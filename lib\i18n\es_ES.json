{"很遗憾您这没有能用的显卡来支持您训练": "Lamentablemente, no tiene una tarjeta gráfica adecuada para soportar su entrenamiento", "是": "Sí", "step1:正在处理数据": "Paso 1: <PERSON><PERSON><PERSON><PERSON>", "step2a:无需提取音高": "Paso 2a: No es necesario extraer el tono", "step2b:正在提取特征": "Paso 2b: Extrayendo características", "step3a:正在训练模型": "Paso 3a: <PERSON><PERSON><PERSON><PERSON> el modelo", "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log": "Entrenamiento finalizado, puede ver el registro de entrenamiento en la consola o en el archivo train.log en la carpeta del experimento", "全流程结束！": "¡Todo el proceso ha terminado!", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "Este software es de código abierto bajo la licencia MIT, el autor no tiene ningún control sobre el software, y aquellos que usan el software y difunden los sonidos exportados por el software son los únicos responsables.<br>Si no está de acuerdo con esta cláusula , no puede utilizar ni citar ningún código ni archivo del paquete de software Consulte el directorio raíz <b>Agreement-LICENSE.txt</b> para obtener más información.", "模型推理": "inferencia del modelo", "推理音色": "inferencia de voz", "刷新音色列表和索引路径": "Actualizar la lista de timbres e índice de rutas", "卸载音色省显存": "Descargue la voz para ahorrar memoria GPU", "请选择说话人id": "seleccione una identificación de altavoz", "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ": "Tecla +12 recomendada para conversión de voz de hombre a mujer, tecla -12 para conversión de voz de mujer a hombre. Si el rango de tono es demasiado amplio y causa distorsión, ajústelo usted mismo a un rango adecuado.", "变调(整数, 半音数量, 升八度12降八度-12)": "Cambio de tono (entero, número de semitonos, subir una octava +12 o bajar una octava -12)", "输入待处理音频文件路径(默认是正确格式示例)": "Ingrese la ruta del archivo del audio que se procesará (el formato predeterminado es el ejemplo correcto)", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU": "Elija el algoritmo de extracción de tono, use 'pm' para acelerar la entrada de canto, 'harvest' es bueno para los graves pero extremadamente lento, 'crepe' tiene buenos resultados pero consume GPU", ">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音": "Si es >=3, entonces use el resultado del reconocimiento de tono de 'harvest' con filtro de mediana, el valor es el radio del filtro, su uso puede debilitar el sonido sordo", "特征检索库文件路径,为空则使用下拉的选择结果": "Ruta del archivo de la biblioteca de características, si está vacío, se utilizará el resultado de la selección desplegable", "自动检测index路径,下拉式选择(dropdown)": "Detección automática de la ruta del índice, selección desplegable (dropdown)", "特征文件路径": "Ruta del archivo de características", "检索特征占比": "Proporción de función de búsqueda", "后处理重采样至最终采样率，0为不进行重采样": "Remuestreo posterior al proceso a la tasa de muestreo final, 0 significa no remuestrear", "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络": "Proporción de fusión para reemplazar el sobre de volumen de entrada con el sobre de volumen de salida, cuanto más cerca de 1, más se utiliza el sobre de salida", "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果": "Proteger las consonantes claras y la respiración, prevenir artefactos como la distorsión de sonido electrónico, 0.5 no está activado, reducir aumentará la protección pero puede reducir el efecto del índice", "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调": "Archivo de curva F0, opcional, un tono por línea, en lugar de F0 predeterminado y cambio de tono", "转换": "Conversión", "输出信息": "Información de salida", "输出音频(右下角三个点,点了可以下载)": "Salida de audio (haga clic en los tres puntos en la esquina inferior derecha para descargar)", "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ": "Conversión por lotes, ingrese la carpeta que contiene los archivos de audio para convertir o cargue varios archivos de audio. El audio convertido se emitirá en la carpeta especificada (opción predeterminada).", "指定输出文件夹": "Especificar carpeta de salida", "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)": "Ingrese la ruta a la carpeta de audio que se procesará (simplemente cópiela desde la barra de direcciones del administrador de archivos)", "也可批量输入音频文件, 二选一, 优先读文件夹": "También se pueden ingresar múltiples archivos de audio, cualquiera de las dos opciones, con prioridad dada a la carpeta", "导出文件格式": "Formato de archivo de exportación", "伴奏人声分离&去混响&去回声": "Separación de voz acompañante & eliminación de reverberación & eco", "人声伴奏分离批量处理， 使用UVR5模型。 <br>合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。 <br>模型分为三类： <br>1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点； <br>2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型； <br> 3、去混响、去延迟模型（by FoxJoy）：<br>  (1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；<br>&emsp;(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。<br>去混响/去延迟，附：<br>1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；<br>2、MDX-Net-Dereverb模型挺慢的；<br>3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "Procesamiento por lotes para la separación de acompañamiento vocal utilizando el modelo UVR5.<br>Ejemplo de formato de ruta de carpeta válido: D:\\ruta\\a\\la\\carpeta\\de\\entrada (copiar desde la barra de direcciones del administrador de archivos).<br>El modelo se divide en tres categorías:<br>1. Preservar voces: Elija esta opción para audio sin armonías. Preserva las voces mejor que HP5. Incluye dos modelos incorporados: HP2 y HP3. HP3 puede filtrar ligeramente el acompañamiento pero conserva las voces un poco mejor que HP2.<br>2. Preservar solo voces principales: Elija esta opción para audio con armonías. Puede debilitar las voces principales. Incluye un modelo incorporado: HP5.<br>3. Modelos de des-reverberación y des-retardo (por FoxJoy):<br>  (1) MDX-Net: La mejor opción para la eliminación de reverberación estéreo pero no puede eliminar la reverberación mono;<br>&emsp;(234) DeEcho: Elimina efectos de retardo. El modo Agresivo elimina más a fondo que el modo Normal. DeReverb adicionalmente elimina la reverberación y puede eliminar la reverberación mono, pero no muy efectivamente para contenido de alta frecuencia fuertemente reverberado.<br>Notas de des-reverberación/des-retardo:<br>1. El tiempo de procesamiento para el modelo DeEcho-DeReverb es aproximadamente el doble que los otros dos modelos DeEcho.<br>2. El modelo MDX-Net-Dereverb es bastante lento.<br>3. La configuración más limpia recomendada es aplicar primero MDX-Net y luego DeEcho-Agresivo.", "输入待处理音频文件夹路径": "Ingrese la ruta a la carpeta de audio que se procesará", "模型": "<PERSON><PERSON>", "指定输出主人声文件夹": "Especifique la carpeta de salida para la voz principal", "指定输出非主人声文件夹": "Especifique la carpeta de salida para las voces no principales", "训练": "Entrenamiento", "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ": "paso 1: Complete la configuración del experimento. Los datos del experimento se almacenan en el directorio 'logs', con cada experimento en una carpeta separada. La ruta del nombre del experimento debe ingresarse manualmente y debe contener la configuración del experimento, los registros y los archivos del modelo entrenado.", "输入实验名": "Ingrese el nombre del modelo", "目标采样率": "Tasa de muestreo objetivo", "模型是否带音高指导(唱歌一定要, 语音可以不要)": "Si el modelo tiene guía de tono (necesaria para cantar, pero no para hablar)", "版本": "Versión", "提取音高和处理数据使用的CPU进程数": "Número de procesos de CPU utilizados para extraer el tono y procesar los datos", "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ": "paso 2a: recorra automáticamente la carpeta de capacitación y corte y normalice todos los archivos de audio que se pueden decodificar en audio. Se generarán dos carpetas 'wav' en el directorio del experimento. Actualmente, solo se admite la capacitación de una sola persona.", "输入训练文件夹路径": "Introduzca la ruta de la carpeta de entrenamiento", "请指定说话人id": "Especifique el ID del hablante", "处理数据": "Procesar da<PERSON>", "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)": "paso 2b: use la CPU para extraer el tono (si el modelo tiene guía de tono) y la GPU para extraer características (seleccione el número de tarjeta).", "以-分隔输入使用的卡号, 例如   0-1-2   使用卡0和卡1和卡2": "Separe los números de identificación de la GPU con '-' al ingresarlos. <PERSON><PERSON> e<PERSON><PERSON><PERSON>, '0-1-2' significa usar GPU 0, GPU 1 y GPU 2.", "显卡信息": "información de la GPU", "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢": "Seleccione el algoritmo de extracción de tono: utilice 'pm' para un procesamiento más rápido de la voz cantada, 'dio' para un discurso de alta calidad pero un procesamiento más lento y 'cosecha' para obtener la mejor calidad pero un procesamiento más lento.", "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程": "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程", "特征提取": "Extracción de características", "step3: 填写训练设置, 开始训练模型和索引": "Paso 3: complete la configuración de entrenamiento y comience a entrenar el modelo y el índice.", "保存频率save_every_epoch": "Frecuencia de guardado (save_every_epoch)", "总训练轮数total_epoch": "Total de épocas de entrenamiento (total_epoch)", "每张显卡的batch_size": "Tam<PERSON><PERSON> del lote (batch_size) por tarjeta gráfica", "是否仅保存最新的ckpt文件以节省硬盘空间": "Si guardar solo el archivo ckpt más reciente para ahorrar espacio en disco", "否": "No", "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速": "Si almacenar en caché todos los conjuntos de entrenamiento en la memoria de la GPU. Los conjuntos de datos pequeños (menos de 10 minutos) se pueden almacenar en caché para acelerar el entrenamiento, pero el almacenamiento en caché de conjuntos de datos grandes puede causar errores de memoria en la GPU y no aumenta la velocidad de manera significativa.", "是否在每次保存时间点将最终小模型保存至weights文件夹": "¿Guardar el pequeño modelo final en la carpeta 'weights' en cada punto de guardado?", "加载预训练底模G路径": "Cargue la ruta G del modelo base preentrenada.", "加载预训练底模D路径": "Cargue la ruta del modelo D base preentrenada.", "训练模型": "<PERSON><PERSON><PERSON>", "训练特征索引": "Índice de características del Entrenamiento", "一键训练": "Entrenamiento con un clic.", "ckpt处理": "Procesamiento de recibos", "模型融合, 可用于测试音色融合": "Fusión de modelos, se puede utilizar para fusionar diferentes voces", "A模型路径": "Modelo A ruta.", "B模型路径": "Modelo B ruta.", "A模型权重": "Un peso modelo para el modelo A.", "模型是否带音高指导": "Si el modelo tiene guía de tono.", "要置入的模型信息": "Información del modelo a colocar.", "保存的模型名不带后缀": "Nombre del modelo guardado sin extensión.", "模型版本型号": "Versión y modelo del modelo", "融合": "Fusión.", "修改模型信息(仅支持weights文件夹下提取的小模型文件)": "Modificar la información del modelo (solo admite archivos de modelos pequeños extraídos en la carpeta de pesos).", "模型路径": "Ruta del modelo", "要改的模型信息": "Información del modelo a modificar", "保存的文件名, 默认空为和源文件同名": "Nombre del archivo que se guardará, el valor predeterminado es el mismo que el nombre del archivo de origen", "修改": "Modificar", "查看模型信息(仅支持weights文件夹下提取的小模型文件)": "Ver información del modelo (solo aplicable a archivos de modelos pequeños extraídos de la carpeta 'pesos')", "查看": "<PERSON>er", "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况": "Extracción de modelo (ingrese la ruta de un archivo de modelo grande en la carpeta 'logs'), aplicable cuando desea extraer un archivo de modelo pequeño después de entrenar a mitad de camino y no se guardó automáticamente, o cuando desea probar un modelo intermedio", "保存名": "Guardar nombre", "模型是否带音高指导,1是0否": "Si el modelo tiene guía de tono, 1 para sí, 0 para no", "提取": "Extracter", "Onnx导出": "Exportar Onnx", "RVC模型路径": "Ruta del modelo RVC", "Onnx输出路径": "<PERSON><PERSON> de salida <PERSON>", "导出Onnx模型": "Exportar modelo Onnx", "常见问题解答": "Preguntas frecuentes", "招募音高曲线前端编辑器": "Reclutar editores front-end para curvas de tono", "加开发群联系我xxxxx": "Únase al grupo de desarrollo para contactarme en xxxxx", "点击查看交流、问题反馈群号": "Haga clic para ver el número de grupo de comunicación y comentarios sobre problemas", "xxxxx": "xxxxx", "加载模型": "<PERSON><PERSON> modelo", "Hubert模型": "<PERSON><PERSON> ", "选择.pth文件": "Seleccionar archivo .pth", "选择.index文件": "Select .index file", "选择.npy文件": "Seleccionar archivo .npy", "输入设备": "Dispositivo de entrada", "输出设备": "Dispositivo de salida", "音频设备(请使用同种类驱动)": "Dispositivo de audio (utilice el mismo tipo de controlador)", "响应阈值": "Umbral de respuesta", "音调设置": "A<PERSON>ste de tono", "Index Rate": "<PERSON><PERSON>", "常规设置": "Configuración general", "采样长度": "Longitud de muestreo", "淡入淡出长度": "Duración del fundido de entrada/salida", "额外推理时长": "Tiempo de inferencia adicional", "输入降噪": "Reducción de ruido de entrada", "输出降噪": "Reducción de ruido de salida", "性能设置": "Configuración de rendimiento", "开始音频转换": "Iniciar conversión de audio", "停止音频转换": "Detener la conversión de audio", "推理时间(ms):": "<PERSON><PERSON><PERSON> tiempo (ms):", "请选择pth文件": "请选择pth文件", "请选择index文件": "请选择index文件", "hubert模型路径不可包含中文": "hubert模型路径不可包含中文", "pth文件路径不可包含中文": "pth文件路径不可包含中文", "index文件路径不可包含中文": "index文件路径不可包含中文", "重载设备列表": "Recargar lista de dispositivos", "音高算法": "音高算法", "harvest进程数": "harvest进程数"}