#!/usr/bin/env python3
"""
Скрипт для запуска RVC FastAPI сервиса с различными опциями
"""

import argparse
import os
import sys
import subprocess


def check_dependencies():
    """Проверка наличия необходимых файлов и зависимостей"""
    print("🔍 Checking dependencies...")

    # Проверка файлов моделей
    required_files = [
        "../weights/w_zaporozhec1_ru.pth",
        "../hubert_base.pt",
        "../rmvpe.pt",
    ]

    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)

    if missing_files:
        print("❌ Missing required model files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        print("\nPlease ensure all model files are in the correct locations.")
        return False

    # Проверка Python пакетов
    try:
        import fastapi
        import uvicorn
        import torch

        print("✅ All dependencies found")
        return True
    except ImportError as e:
        print(f"❌ Missing Python package: {e}")
        print("Please install dependencies: pip install -r requirements.txt")
        return False


def run_development(host="127.0.0.1", port=8000, reload=True):
    """Запуск в режиме разработки"""
    print(f"🚀 Starting development server on {host}:{port}")

    cmd = ["uvicorn", "main:app", "--host", host, "--port", str(port)]

    if reload:
        cmd.append("--reload")

    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except subprocess.CalledProcessError as e:
        print(f"❌ Server failed to start: {e}")
        sys.exit(1)


def run_production(host="0.0.0.0", port=8000, workers=1):
    """Запуск в продакшн режиме"""
    print(f"🚀 Starting production server on {host}:{port} with {workers} workers")

    cmd = [
        "uvicorn",
        "main:app",
        "--host",
        host,
        "--port",
        str(port),
        "--workers",
        str(workers),
        "--access-log",
        "--no-use-colors",
    ]

    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except subprocess.CalledProcessError as e:
        print(f"❌ Server failed to start: {e}")
        sys.exit(1)


def run_docker():
    """Запуск через Docker"""
    print("🐳 Starting with Docker...")

    try:
        subprocess.run(["docker-compose", "up", "--build"], check=True)
    except KeyboardInterrupt:
        print("\n👋 Stopping Docker containers...")
        subprocess.run(["docker-compose", "down"])
    except subprocess.CalledProcessError as e:
        print(f"❌ Docker failed: {e}")
        sys.exit(1)


def main():
    parser = argparse.ArgumentParser(description="RVC FastAPI Server Runner")

    parser.add_argument(
        "--mode",
        choices=["dev", "prod", "docker"],
        default="dev",
        help="Run mode (default: dev)",
    )

    parser.add_argument(
        "--host", default="127.0.0.1", help="Host to bind to (default: 127.0.0.1)"
    )

    parser.add_argument(
        "--port", type=int, default=8000, help="Port to bind to (default: 8000)"
    )

    parser.add_argument(
        "--workers",
        type=int,
        default=1,
        help="Number of worker processes for production mode (default: 1)",
    )

    parser.add_argument(
        "--no-reload",
        action="store_true",
        help="Disable auto-reload in development mode",
    )

    parser.add_argument(
        "--skip-checks", action="store_true", help="Skip dependency checks"
    )

    args = parser.parse_args()

    # Проверка зависимостей
    if not args.skip_checks and not check_dependencies():
        sys.exit(1)

    # Запуск в соответствующем режиме
    if args.mode == "dev":
        run_development(host=args.host, port=args.port, reload=not args.no_reload)
    elif args.mode == "prod":
        run_production(host=args.host, port=args.port, workers=args.workers)
    elif args.mode == "docker":
        run_docker()


if __name__ == "__main__":
    main()
