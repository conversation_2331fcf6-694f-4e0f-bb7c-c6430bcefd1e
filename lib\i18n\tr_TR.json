{"很遗憾您这没有能用的显卡来支持您训练": "<PERSON><PERSON><PERSON>, eğitiminizi desteklemek için uyumlu bir GPU bulunmamaktadır.", "是": "<PERSON><PERSON>", "step1:正在处理数据": "Adım 1: <PERSON><PERSON>", "step2a:无需提取音高": "Adım 2a: <PERSON><PERSON> çıkartma adımını atlama", "step2b:正在提取特征": "Adım 2b: <PERSON><PERSON><PERSON><PERSON><PERSON>", "step3a:正在训练模型": "Adım 3a: <PERSON> e<PERSON><PERSON><PERSON> ba<PERSON><PERSON>ı", "训练结束, 您可查看控制台训练日志或实验文件夹下的train.log": "Eğitim tamamlandı. Eğitim günlüklerini konsolda veya deney klasörü altındaki train.log dosyasında kontrol edebilirsiniz.", "全流程结束！": "Tüm işlemler tamamlandı!", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MIT lisansı altında açık kaynaklıdır. Yazarın yazılım üzerinde herhangi bir kontrolü yoktur. Yazılımı kullanan ve yazılım tarafından dışa aktarılan sesleri dağıtan kullanıcılar sorumludur. <br><PERSON>ğer bu maddeyle aynı fikirde değilseniz, yazılım paketi içindeki herhangi bir kod veya dosyayı kullanamaz veya referans göremezsiniz. Detaylar için kök dizindeki <b>Agreement-LICENSE.txt</b> dosyasına bakınız.", "模型推理": "<PERSON> (Inference)", "推理音色": "<PERSON><PERSON> (Inference):", "刷新音色列表和索引路径": "Ses listesini ve indeks yolunu yenile", "卸载音色省显存": "GPU bellek kullanımını azaltmak için sesi kaldır", "请选择说话人id": "Konuşmacı/Şarkıcı No seçin:", "男转女推荐+12key, 女转男推荐-12key, 如果音域爆炸导致音色失真也可以自己调整到合适音域. ": "Erkekten kadına çevirmek için +12 tuş önerilir, kadından erkeğe çevirmek için ise -12 tuş önerilir. <PERSON>ğ<PERSON> ses aralığı çok fazla genişler ve ses bozulursa, is<PERSON>ğe bağlı olarak uygun aralığa kendiniz de ayarlayabilirsiniz.", "变调(整数, 半音数量, 升八度12降八度-12)": "Transpoze et (tams<PERSON><PERSON>, yarı<PERSON> say<PERSON>; bir oktav yükseltmek için: 12, bir oktav düşürmek için: -12):", "输入待处理音频文件路径(默认是正确格式示例)": "İşlenecek ses dosyasının yolunu girin (varsayılan doğru format örneğidir):", "选择音高提取算法,输入歌声可用pm提速,harvest低音好但巨慢无比,crepe效果好但吃GPU,rmvpe效果最好且微吃GPU": "Pitch algoritmasını seçin ('pm': daha hız<PERSON>ı <PERSON>ıkarır ancak daha düşük kaliteli konuşma; 'harvest': daha iyi konuşma sesi ancak son derece ya<PERSON>; 'crepe': daha da iyi kalite ancak GPU yoğunluğu gerektirir):", ">=3则使用对harvest音高识别的结果使用中值滤波，数值为滤波半径，使用可以削弱哑音": "Eğer >=3 ise, elde edilen pitch sonuçlarına median filtreleme uygula. <PERSON><PERSON> <PERSON><PERSON><PERSON>, filtre yarıçapını temsil eder ve nefesliliği azaltabilir.", "特征检索库文件路径,为空则使用下拉的选择结果": "Özellik indeksi dosyasının yolunu belirtin. Seçilen sonucu kullanmak için boş bırakın veya açılır menüden seçim yapın.", "自动检测index路径,下拉式选择(dropdown)": "İndeks yolunu otomatik olarak tespit et ve açılır menüden seçim yap.", "特征文件路径": "Özellik dosyasının yolu:", "检索特征占比": "<PERSON>ma <PERSON> oranı (vurgu gücünü kontrol eder, çok yüksek olması sanal etkilere neden olur)", "后处理重采样至最终采样率，0为不进行重采样": "Son işleme aşamasında çıktı sesini son örnekleme hızına yeniden örnekle. 0 değeri için yeniden örnekleme yapılmaz:", "输入源音量包络替换输出音量包络融合比例，越靠近1越使用输出包络": "Sesin hacim zarfını ayarlayın. 0'a yakın de<PERSON><PERSON><PERSON>, sesin orijinal vokallerin hacmine benzer olmasını sağlar. Düşük bir değerle ses gürültüsünü maskeleyebilir ve hacmi daha doğal bir şekilde duyulabilir hale getirebilirsiniz. 1'e yaklaştıkça sürekli bir yüksek ses seviyesi elde edilir:", "保护清辅音和呼吸声，防止电音撕裂等artifact，拉满0.5不开启，调低加大保护力度但可能降低索引效果": "Sessiz ünsüzleri ve nefes seslerini koruyarak elektronik müzikte yırtılma gibi sanal hataların oluşmasını engeller. 0.5 olarak ayarlandığında devre dışı kalır. Değerin azaltılması korumayı artırabilir, ancak indeksleme doğruluğunu azaltabilir:", "F0曲线文件, 可选, 一行一个音高, 代替默认F0及升降调": "F0 eğ<PERSON><PERSON> (isteğe bağlı). Her satırda bir pitch değeri bulunur. Varsayılan F0 ve pitch modülasyonunu değiştirir:", "转换": "Dönüş<PERSON>ür", "输出信息": "Çıkış bilgisi", "输出音频(右下角三个点,点了可以下载)": "Ses dosyasını dışa aktar (indirmek için sağ alt köşedeki üç noktaya tıklayın)", "批量转换, 输入待转换音频文件夹, 或上传多个音频文件, 在指定文件夹(默认opt)下输出转换的音频. ": "Toplu dönüştür. Dönüştürülecek ses dosyalarının bulunduğu klasörü girin veya birden çok ses dosyasını yükleyin. Dönüştürülen ses dosyaları belirtilen klasöre ('opt' varsayılan olarak) dönüştürülecektir", "指定输出文件夹": "Çıkış klasörünü belirt:", "输入待处理音频文件夹路径(去文件管理器地址栏拷就行了)": "İşlenecek ses klasörünün yolunu girin (dosya yöneticisinin adres <PERSON>ğundan kopyalayın):", "也可批量输入音频文件, 二选一, 优先读文件夹": "Toplu olarak ses dosyalarını da girebilirsiniz. İki seçenekten birini seçin. Öncelik klasörden okumaya verilir.", "导出文件格式": "Dışa aktarma dosya formatı", "伴奏人声分离&去混响&去回声": "Vokal/Müzik Ayrıştırma ve Yankı Giderme", "人声伴奏分离批量处理， 使用UVR5模型。 <br>合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。 <br>模型分为三类： <br>1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点； <br>2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型； <br> 3、去混响、去延迟模型（by FoxJoy）：<br>  (1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；<br>&emsp;(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。<br>去混响/去延迟，附：<br>1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；<br>2、MDX-Net-Dereverb模型挺慢的；<br>3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "Batch işleme kullanarak vokal eşlik ayrımı için UVR5 modeli kullanılır.<br>Geçerli bir klasör yol formatı örneği: D:\\path\\to\\input\\folder (dosya yöneticisi adres çubuğundan kopyalanır).<br>Model üç kategoriye ayrılır:<br>1. Vokalleri koru: <PERSON><PERSON> se<PERSON>, harmoni içermeyen sesler için kullanın. HP5'ten daha iyi bir şekilde vokalleri korur. İki dahili model içerir: HP2 ve HP3. HP3, eşlik sesini hafifçe sızdırabilir, ancak vokalleri HP2'den biraz daha iyi korur.<br>2. Sadece ana vokalleri koru: <PERSON><PERSON> se<PERSON>ği, harmoni içeren sesler için kullanın. Ana vokalleri zayıflatabilir. Bir dahili model içerir: HP5.<br>3. Reverb ve gecikme modelleri (FoxJoy tarafından):<br>  (1) MDX-Net: Stereo reverb'i kaldırmak için en iyi seçenek, ancak mono reverb'i kaldıramaz;<br> (234) DeEcho: Gecikme efektlerini kaldırır. Agresif mod, Normal moda göre daha kapsamlı bir şekilde kaldırma yapar. DeReverb ayrıca reverb'i kaldırır ve mono reverb'i kaldırabilir, ancak yoğun yankılı yüksek frekanslı içerikler için çok etkili değildir.<br>Reverb/gecikme notları:<br>1. DeEcho-DeReverb modelinin işleme süresi diğer iki DeEcho modeline göre yaklaşık olarak iki kat daha uzundur.<br>2. MDX-Net-Dereverb modeli oldukça yavaştır.<br>3. Tavsiye edilen en temiz yapılandırma önce MDX-Net'i uygulamak ve ardından DeEcho-Aggressive uygulamaktır.", "输入待处理音频文件夹路径": "İşlenecek ses klasörünün yolunu girin:", "模型": "Model", "指定输出主人声文件夹": "Vokal için çı<PERSON> klasörünü belirtin:", "指定输出非主人声文件夹": "Müzik ve diğer sesler için çıkış klasörünü belirtin:", "训练": "Eğitim", "step1: 填写实验配置. 实验数据放在logs下, 每个实验一个文件夹, 需手工输入实验名路径, 内含实验配置, 日志, 训练得到的模型文件. ": "Adım 1: Den<PERSON>sel yapılandırmayı doldurun. Deneysel veriler 'logs' klasöründe saklanır ve her bir deney için ayrı bir klasör vardır. Deneysel adı yolu manuel olarak girin; bu yol, deney<PERSON> yapılandırmayı, g<PERSON><PERSON><PERSON><PERSON><PERSON>ri ve eğitilmiş model dosyalarını içerir.", "输入实验名": "Deneysel adı girin:", "目标采样率": "<PERSON><PERSON><PERSON> oran<PERSON>:", "模型是否带音高指导(唱歌一定要, 语音可以不要)": "Modelin ses yü<PERSON>i (Pitch) rehberliği içerip içermediği (şarkı söyleme için <PERSON>ı<PERSON>, konuşma için isteğe bağlıdır):", "版本": "S<PERSON>r<PERSON><PERSON>", "提取音高和处理数据使用的CPU进程数": "Ses yüksekliği çıkartmak (Pitch) ve verileri işlemek için kullanılacak CPU işlemci sayısı:", "step2a: 自动遍历训练文件夹下所有可解码成音频的文件并进行切片归一化, 在实验目录下生成2个wav文件夹; 暂时只支持单人训练. ": "Adım 2a: Eğitim klasöründe ses dosyalarını otomatik olarak gezinerek dilimleme normalizasyonu yapın. Deney dizini içinde 2 wav klasörü oluşturur. <PERSON><PERSON> anda sadece tek kişilik eğitim desteklenmektedir.", "输入训练文件夹路径": "Eğitim klasörünün yolunu girin:", "请指定说话人id": "Lütfen konuşmacı/sanatçı no belirtin:", "处理数据": "Verileri işle", "step2b: 使用CPU提取音高(如果模型带音高), 使用GPU提取特征(选择卡号)": "Adım 2b: <PERSON><PERSON> <PERSON><PERSON><PERSON> (Pitch) çıkartmak için CPU kullanın (eğer model ses yüksekliği içeriyorsa), özellikleri çıkartmak için GPU kullanın (GPU indeksini seçin):", "以-分隔输入使用的卡号, 例如   0-1-2   使用卡0和卡1和卡2": "GPU indekslerini '-' <PERSON><PERSON> <PERSON>, <PERSON><PERSON><PERSON><PERSON> 0-1-2, GPU 0, 1 ve 2'yi kullanmak için:", "显卡信息": "GPU Bilgisi", "选择音高提取算法:输入歌声可用pm提速,高质量语音但CPU差可用dio提速,harvest质量更好但慢": "Ses yü<PERSON> (Pitch) çıkartma algoritmasını seçin ('pm': daha hı<PERSON><PERSON>, ancak düşük kaliteli konuşma; 'dio': geliştirilmiş konuşma kalitesi, ancak daha yavaş çıkartma; 'harvest': daha iyi kalite, ancak daha da yavaş çıkartma):", "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程": "rmvpe卡号配置：以-分隔输入使用的不同进程卡号,例如0-0-1使用在卡0上跑2个进程并在卡1上跑1个进程", "特征提取": "Özellik çıkartma", "step3: 填写训练设置, 开始训练模型和索引": "Adım 3: <PERSON><PERSON><PERSON><PERSON> ayarlarını doldurun ve modeli ve dizini eğitmeye başlayın", "保存频率save_every_epoch": "<PERSON><PERSON><PERSON> sıklığı (save_every_epoch):", "总训练轮数total_epoch": "Toplam eğitim turu (total_epoch):", "每张显卡的batch_size": "Her GPU için yı<PERSON>ın boyutu (batch_size):", "是否仅保存最新的ckpt文件以节省硬盘空间": "Sadece en son '.ckpt' dosyasını kaydet:", "否": "Hay<PERSON><PERSON>", "是否缓存所有训练集至显存. 10min以下小数据可缓存以加速训练, 大数据缓存会炸显存也加不了多少速": "Tüm eğitim verilerini GPU belleğine önbelleğe alıp almayacağınızı belirtin. Küçük veri setlerini (10 dakikadan az) önbelleğe almak eğitimi hızlandırabilir, ancak büyük veri setlerini önbelleğe almak çok fazla GPU belleği tüketir ve çok fazla hız artışı sağlamaz:", "是否在每次保存时间点将最终小模型保存至weights文件夹": "Her kaydetme noktasında son kü<PERSON><PERSON><PERSON> bir modeli 'weights' klasörüne kaydetmek için:", "加载预训练底模G路径": "Önceden eğitilmiş temel G modelini yükleme yolu:", "加载预训练底模D路径": "Önceden eğitilmiş temel D modelini yükleme yolu:", "训练模型": "<PERSON><PERSON>", "训练特征索引": "Özellik Dizinini Eğit", "一键训练": "Tek Tuşla Eğit", "ckpt处理": "ckpt İşleme", "模型融合, 可用于测试音色融合": "Model birle<PERSON><PERSON><PERSON><PERSON>, ses rengi birleştirm<PERSON> i<PERSON><PERSON> k<PERSON>", "A模型路径": "A Modeli Yolu:", "B模型路径": "B Modeli Yolu:", "A模型权重": "A Modeli Ağırlığı:", "模型是否带音高指导": "Modelin ses yüksekliği rehberi içerip içermediği:", "要置入的模型信息": "Eklemek için model bilgileri:", "保存的模型名不带后缀": "Kaydedilecek model adı (uzantı olmadan):", "模型版本型号": "Model mi<PERSON><PERSON>:", "融合": "Birleştir", "修改模型信息(仅支持weights文件夹下提取的小模型文件)": "Model bilgi<PERSON><PERSON> düzenle (sadece 'weights' klasöründen çıkarılan küçük model dosyalar<PERSON> desteklenir)", "模型路径": "Model Yolu:", "要改的模型信息": "Düzenlenecek model bilgileri:", "保存的文件名, 默认空为和源文件同名": "Kaydedilecek dosya adı (varsayılan: kaynak dosya ile aynı):", "修改": "<PERSON><PERSON><PERSON><PERSON>", "查看模型信息(仅支持weights文件夹下提取的小模型文件)": "Model bilgilerini gö<PERSON><PERSON><PERSON><PERSON><PERSON> (sadece 'weights' klasöründen çıkarılan küçük model dosyalar<PERSON> desteklenir)", "查看": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "模型提取(输入logs文件夹下大文件模型路径),适用于训一半不想训了模型没有自动提取保存小文件模型,或者想测试中间模型的情况": "Model çıkartma (büyük dosya modeli yolunu 'logs' klasöründe girin). <PERSON><PERSON>, eğitimi yarıda bırakmak istediğinizde ve manuel olarak küçük bir model dosyası çıkartmak ve kaydetmek istediğinizde veya bir ara modeli test etmek istediğinizde kullanışlıdır:", "保存名": "<PERSON><PERSON><PERSON>:", "模型是否带音高指导,1是0否": "Modelin ses yüksekliği rehberi içerip içermediği (1: evet, 0: hay<PERSON>r):", "提取": "<PERSON><PERSON><PERSON><PERSON>", "Onnx导出": "Onnx Dışa Aktar", "RVC模型路径": "RVC Model Yolu:", "Onnx输出路径": "Onnx Dışa Aktarım Yolu:", "导出Onnx模型": "Onnx Modeli Dışa Aktar", "常见问题解答": "Sıkça Sorulan Sorular (SSS)", "招募音高曲线前端编辑器": "Ses yükseklik eğrisi ön uç düzenleyicisi için işe alım", "加开发群联系我xxxxx": "Geliştirme grubuna katılın ve benimle iletişime geçin: xxxxx", "点击查看交流、问题反馈群号": "İletişim ve sorun geri bildirim grup numarasını görüntülemek için tıklayın", "xxxxx": "xxxxx", "加载模型": "<PERSON> <PERSON><PERSON><PERSON>", "Hubert模型": "<PERSON>", "选择.pth文件": ".pth dosyası seç", "选择.index文件": ".index dosyası seç", "选择.npy文件": ".npy dosyası seç", "输入设备": "Giriş cihazı", "输出设备": "Çıkış cihazı", "音频设备(请使用同种类驱动)": "<PERSON>s cihazı (aynı tür sürücüyü kullanın)", "响应阈值": "Tepki eşiği", "音调设置": "Pitch ayarları", "Index Rate": "Index Oranı", "常规设置": "<PERSON><PERSON>", "采样长度": "Örnekleme uzunluğu", "淡入淡出长度": "Geçiş (Fade) uzunluğu", "额外推理时长": "Ekstra çıkartma süresi", "输入降噪": "<PERSON><PERSON><PERSON> gürültü azaltma", "输出降噪": "Çıkış gürültü azaltma", "性能设置": "Performans ayarları", "开始音频转换": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON>", "停止音频转换": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> durdur", "推理时间(ms):": "<PERSON><PERSON><PERSON><PERSON><PERSON> (ms):", "请选择pth文件": "Lütfen .pth dosyası seçin", "请选择index文件": "Lütfen .index dosyası seçin", "hubert模型路径不可包含中文": "hubert modeli yolu Çince karakter içeremez", "pth文件路径不可包含中文": ".pth dosya yolu Çince karakter içeremez", "index文件路径不可包含中文": ".index dosya yolu Çince karakter içeremez", "重载设备列表": "Cihaz listesini ye<PERSON>", "音高算法": "音高算法", "harvest进程数": "harvest进程数"}