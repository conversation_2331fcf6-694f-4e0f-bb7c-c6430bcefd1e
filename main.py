from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.responses import FileResponse
import tempfile
import os
import shutil
from typing import Optional
import uvicorn
import logging
from contextlib import asynccontextmanager

from models import VoiceConversionRequest
from voice_converter import VoiceConverter
from config import get_settings

# Настройка логирования
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Глобальная переменная для конвертера
voice_converter: Optional[VoiceConverter] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Код, который выполнится перед запуском приложения
    global voice_converter
    logger.info("Application startup...")
    settings = get_settings()
    voice_converter = VoiceConverter(settings)
    await voice_converter.initialize()
    logger.info("Voice converter initialized.")
    yield
    # Код, который выполнится после остановки приложения
    logger.info("Application shutdown...")
    if voice_converter:
        voice_converter.cleanup()
        logger.info("Cleanup complete.")


# Инициализация приложения
app = FastAPI(
    title="RVC Voice Conversion API",
    description="API для конвертации голоса с использованием RVC и RMVPE",
    version="1.0.0",
    lifespan=lifespan,
)


@app.get("/")
async def root():
    """Корневой эндпойнт"""
    return {"message": "RVC Voice Conversion API", "status": "running"}


@app.get("/health")
async def health_check():
    """Проверка состояния сервиса"""
    return {"status": "healthy", "model_loaded": voice_converter is not None}


@app.post("/convert")
async def convert_voice(
    audio_file: UploadFile = File(..., description="Аудио файл для конвертации"),
    f0_up_key: int = 0,
    index_rate: float = 0.75,
    filter_radius: int = 3,
    rms_mix_rate: float = 0.25,
    protect: float = 0.33,
    f0_method: str = "rmvpe",
):
    """
    Конвертация голоса с использованием RVC и RMVPE

    - **audio_file**: Входной аудио файл (WAV, MP3, FLAC)
    - **f0_up_key**: Изменение тональности в полутонах (-12 до +12). Мужской->женский: +12, женский->мужской: -12
    - **index_rate**: Коэффициент использования индекса (0.0-1.0). Рекомендуется 0.75
    - **filter_radius**: Радиус медианного фильтра (0-7). >=3 рекомендуется для harvest
    - **rms_mix_rate**: Коэффициент смешивания RMS (0.0-1.0). Рекомендуется 0.25
    - **protect**: Защита согласных звуков (0.0-0.5). 0.33 - рекомендуется, 0.5 - отключено
    - **f0_method**: Метод извлечения f0 (pm, harvest, crepe, rmvpe). Рекомендуется rmvpe
    """
    logger.info(
        f"Received conversion request for file: {audio_file.filename} ({audio_file.content_type})"
    )
    if not voice_converter:
        logger.error("Voice converter not initialized.")
        raise HTTPException(status_code=503, detail="Voice converter not initialized")

    # Проверка типа файла (более гибкая проверка)
    allowed_types = [
        "audio/",
        "application/octet-stream",
    ]  # curl может отправлять как octet-stream
    if not any(audio_file.content_type.startswith(t) for t in allowed_types):
        logger.warning(f"Invalid content type: {audio_file.content_type}")
        raise HTTPException(status_code=400, detail="Файл должен быть аудио")

    # Создание временных файлов
    with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as input_temp:
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as output_temp:
            try:
                # Сохранение входного файла
                logger.info(
                    f"Saving uploaded file to temporary path: {input_temp.name}"
                )
                shutil.copyfileobj(audio_file.file, input_temp)
                input_temp.flush()
                logger.info("File saved successfully.")

                # Создание запроса
                request = VoiceConversionRequest(
                    f0_up_key=f0_up_key,
                    index_rate=index_rate,
                    filter_radius=filter_radius,
                    rms_mix_rate=rms_mix_rate,
                    protect=protect,
                    f0_method=f0_method,
                )
                logger.info(
                    f"Created conversion request with params: {request.model_dump()}"
                )

                # Конвертация
                logger.info(
                    f"Starting voice conversion from {input_temp.name} to {output_temp.name}"
                )
                result = await voice_converter.convert(
                    input_temp.name, output_temp.name, request
                )
                logger.info(
                    f"Conversion finished in {result.processing_time}s. Success: {result.success}"
                )

                if not result.success:
                    logger.error(f"Conversion failed: {result.error_message}")
                    raise HTTPException(status_code=500, detail=result.error_message)

                # Возврат файла
                logger.info(f"Returning converted file: {output_temp.name}")
                return FileResponse(
                    output_temp.name,
                    media_type="audio/wav",
                    filename="converted_audio.wav",
                    headers={"X-Processing-Time": str(result.processing_time)},
                )

            except Exception as e:
                logger.exception("An error occurred during conversion.")
                raise HTTPException(
                    status_code=500, detail=f"Ошибка конвертации: {str(e)}"
                )
            finally:
                # Очистка временных файлов
                try:
                    if os.path.exists(input_temp.name):
                        logger.info(
                            f"Cleaning up temporary input file: {input_temp.name}"
                        )
                        os.unlink(input_temp.name)
                except Exception as e:
                    logger.warning(
                        f"Could not clean up input file {input_temp.name}: {e}"
                    )

                # Очистка выходного файла (если конвертация не удалась)
                try:
                    if os.path.exists(output_temp.name):
                        os.unlink(output_temp.name)
                except Exception as e:
                    logger.warning(
                        f"Could not clean up output file {output_temp.name}: {e}"
                    )


if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8000)
