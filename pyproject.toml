[project]
name = "fastapi-rmvpe"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "datasets>=4.0.0",
    "fairseq>=0.12.2",
    "faiss-cpu>=1.7.0",
    "fastapi>=0.100.0",
    "ffmpeg-python>=0.2.0",
    "librosa>=0.10.0",
    "numpy>=1.24.0,<2.0.0",
    "praat-parselmouth>=0.4.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "python-multipart>=0.0.6",
    "pyworld>=0.3.0",
    "requests>=2.32.4",
    "scipy>=1.10.0",
    "soundfile>=0.12.0",
    "torch>=2.0.0",
    "torchaudio>=2.0.0",
    "torchcrepe>=0.0.20",
    "tqdm>=4.60.0",
    "transformers>=4.53.2",
    "uvicorn>=0.20.0",
]
