#!/usr/bin/env python3
"""
Тестовый скрипт для проверки API конвертации голоса
"""

import requests
import os
import time

def test_voice_conversion():
    """Тестирование API конвертации голоса"""
    
    # URL API
    api_url = "http://127.0.0.1:8000/convert"
    
    # Путь к тестовому файлу
    test_file = "1093.mp3"
    
    if not os.path.exists(test_file):
        print(f"Ошибка: Тестовый файл {test_file} не найден")
        return False
    
    print(f"Тестирование конвертации файла: {test_file}")
    
    # Параметры конвертации
    params = {
        "f0_up_key": 0,
        "index_rate": 0.75,
        "filter_radius": 3,
        "rms_mix_rate": 0.25,
        "protect": 0.33,
        "f0_method": "rmvpe"
    }
    
    # Подготовка файла для отправки
    with open(test_file, 'rb') as f:
        files = {'audio_file': (test_file, f, 'audio/mpeg')}
        
        print("Отправка запроса на конвертацию...")
        start_time = time.time()
        
        try:
            response = requests.post(api_url, files=files, data=params, timeout=120)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            print(f"Время обработки: {processing_time:.2f} секунд")
            print(f"Статус ответа: {response.status_code}")
            
            if response.status_code == 200:
                # Сохранение результата
                output_file = "converted_output.wav"
                with open(output_file, 'wb') as out_f:
                    out_f.write(response.content)
                
                file_size = len(response.content)
                print(f"Конвертация успешна!")
                print(f"Размер выходного файла: {file_size} байт")
                print(f"Результат сохранен в: {output_file}")
                
                # Проверка заголовков
                if 'X-Processing-Time' in response.headers:
                    server_time = response.headers['X-Processing-Time']
                    print(f"Время обработки на сервере: {server_time} секунд")
                
                return True
            else:
                print(f"Ошибка: {response.status_code}")
                print(f"Ответ сервера: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"Ошибка при отправке запроса: {e}")
            return False

def test_health_check():
    """Проверка состояния сервера"""
    try:
        response = requests.get("http://127.0.0.1:8000/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("Состояние сервера:")
            print(f"  Статус: {data.get('status', 'unknown')}")
            print(f"  Модель загружена: {data.get('model_loaded', False)}")
            return True
        else:
            print(f"Ошибка проверки состояния: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"Ошибка подключения к серверу: {e}")
        return False

if __name__ == "__main__":
    print("=== Тестирование API конвертации голоса ===")
    
    # Проверка состояния сервера
    print("\n1. Проверка состояния сервера...")
    if not test_health_check():
        print("Сервер недоступен. Убедитесь, что сервер запущен.")
        exit(1)
    
    # Тестирование конвертации
    print("\n2. Тестирование конвертации голоса...")
    success = test_voice_conversion()
    
    if success:
        print("\n✅ Тест прошел успешно!")
    else:
        print("\n❌ Тест не прошел!")
        exit(1)
