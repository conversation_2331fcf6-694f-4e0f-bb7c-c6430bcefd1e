version: '3.8'

services:
  rvc-api:
    build: .
    ports:
      - "8000:8000"
    volumes:
      # Монтируем модели из родительской директории
      - ../weights:/app/weights:ro
      - ../hubert_base.pt:/app/hubert_base.pt:ro
      - ../rmvpe.pt:/app/rmvpe.pt:ro
      - ../pretrained_v2:/app/pretrained_v2:ro
      # Опционально: индексные файлы
      - ../logs:/app/logs:ro
    environment:
      - RVC_DEVICE=cpu  # Измените на cuda:0 если есть GPU
      - RVC_IS_HALF=false  # Измените на true для GPU
      - RVC_LOG_LEVEL=INFO
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Опциональный nginx для проксирования (для продакшена)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - rvc-api
    restart: unless-stopped
    profiles:
      - production
