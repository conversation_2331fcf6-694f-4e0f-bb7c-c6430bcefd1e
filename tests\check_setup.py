#!/usr/bin/env python3
"""
Скрипт для проверки корректности настройки RVC FastAPI сервиса
"""

import os
import sys
import traceback
from pathlib import Path

# Добавляем текущую директорию в путь
sys.path.insert(0, str(Path(__file__).parent))


def check_file_exists(file_path: str, description: str) -> bool:
    """Проверка существования файла"""
    if os.path.exists(file_path):
        size = os.path.getsize(file_path)
        print(f"✅ {description}: {file_path} ({size:,} bytes)")
        return True
    else:
        print(f"❌ {description}: {file_path} - NOT FOUND")
        return False


def check_python_imports():
    """Проверка импорта Python модулей"""
    print("\n🔍 Checking Python imports...")

    required_modules = [
        ("fastapi", "FastAPI framework"),
        ("uvicorn", "ASGI server"),
        ("pydantic", "Data validation"),
        ("torch", "PyTorch"),
        ("numpy", "NumPy"),
        ("librosa", "Audio processing"),
        ("scipy", "Scientific computing"),
        ("fairseq", "Fairseq (for Hubert)"),
    ]

    all_good = True
    for module_name, description in required_modules:
        try:
            __import__(module_name)
            print(f"✅ {description}: {module_name}")
        except ImportError as e:
            print(f"❌ {description}: {module_name} - {e}")
            all_good = False

    return all_good


def check_config():
    """Проверка конфигурации"""
    print("\n🔍 Checking configuration...")

    try:
        from config import get_settings

        settings = get_settings()

        print(f"✅ Device: {settings.device}")
        print(f"✅ Half precision: {settings.is_half}")
        print(f"✅ Target sample rate: {settings.target_sample_rate}")
        print(f"✅ F0 method: {settings.default_f0_method}")

        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        traceback.print_exc()
        return False


def check_models():
    """Проверка моделей"""
    print("\n🔍 Checking model files...")

    # Относительные пути от fastapi директории
    model_files = [
        ("../weights/w_zaporozhec1_ru.pth", "RVC Voice Model"),
        ("../hubert_base.pt", "Hubert Base Model"),
        ("../rmvpe.pt", "RMVPE Model"),
    ]

    all_good = True
    for file_path, description in model_files:
        if not check_file_exists(file_path, description):
            all_good = False

    return all_good


def check_voice_converter():
    """Проверка инициализации VoiceConverter"""
    print("\n🔍 Checking VoiceConverter initialization...")

    try:
        from config import get_settings
        from voice_converter import VoiceConverter

        settings = get_settings()
        converter = VoiceConverter(settings)

        print("✅ VoiceConverter created successfully")
        print("⚠️  Note: Full initialization requires calling initialize() method")

        return True
    except Exception as e:
        print(f"❌ VoiceConverter error: {e}")
        traceback.print_exc()
        return False


def check_fastapi_app():
    """Проверка FastAPI приложения"""
    print("\n🔍 Checking FastAPI application...")

    try:
        from main import app

        # Проверка эндпойнтов
        routes = [route.path for route in app.routes]
        expected_routes = ["/", "/health", "/convert"]

        for route in expected_routes:
            if route in routes:
                print(f"✅ Route: {route}")
            else:
                print(f"❌ Route missing: {route}")
                return False

        print("✅ FastAPI application structure OK")
        return True

    except Exception as e:
        print(f"❌ FastAPI application error: {e}")
        traceback.print_exc()
        return False


def main():
    """Основная функция проверки"""
    print("🚀 RVC FastAPI Setup Checker")
    print("=" * 50)

    checks = [
        ("Model Files", check_models),
        ("Python Imports", check_python_imports),
        ("Configuration", check_config),
        ("VoiceConverter", check_voice_converter),
        ("FastAPI App", check_fastapi_app),
    ]

    passed = 0
    total = len(checks)

    for check_name, check_func in checks:
        print(f"\n{'=' * 20} {check_name} {'=' * 20}")
        try:
            if check_func():
                passed += 1
            else:
                print(f"❌ {check_name} check failed")
        except Exception as e:
            print(f"❌ {check_name} check crashed: {e}")

    # Итоговый результат
    print(f"\n{'=' * 50}")
    print("SETUP CHECK RESULTS")
    print(f"{'=' * 50}")
    print(f"Passed: {passed}/{total}")

    if passed == total:
        print("🎉 All checks passed! Ready to run the API server.")
        print("\nTo start the server:")
        print("  python run.py --mode dev")
        print("  or")
        print("  python main.py")
        return True
    else:
        print("❌ Some checks failed. Please fix the issues above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
