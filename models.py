from pydantic import BaseModel, Field
from typing import Optional


class VoiceConversionRequest(BaseModel):
    """Модель запроса для конвертации голоса (параметры из docs_project Gradio интерфейса)"""

    f0_up_key: int = Field(
        default=0,
        ge=-12,
        le=12,
        description="Изменение тональности в полутонах. Мужской->женский: +12, женский->мужской: -12",
    )

    index_rate: float = Field(
        default=0.75,
        ge=0.0,
        le=1.0,
        description="Коэффициент использования индекса для поиска признаков. 0.75 - рекомендуемое значение",
    )

    filter_radius: int = Field(
        default=3,
        ge=0,
        le=7,
        description="Радиус медианного фильтра для harvest. >=3 рекомендуется для сглаживания",
    )

    rms_mix_rate: float = Field(
        default=0.25,
        ge=0.0,
        le=1.0,
        description="Смешивание RMS огибающей. 0.25 - рекомендуемое значение",
    )

    protect: float = Field(
        default=0.33,
        ge=0.0,
        le=0.5,
        description="Защита согласных и дыхания. 0.33 - рекомендуемое, 0.5 - отключено",
    )

    f0_method: Optional[str] = Field(
        default="rmvpe",
        description="Метод извлечения f0: pm, harvest, crepe, rmvpe (рекомендуется rmvpe)",
    )


class VoiceConversionResponse(BaseModel):
    """Модель ответа конвертации голоса"""

    success: bool = Field(description="Успешность операции")

    processing_time: Optional[float] = Field(
        default=None, description="Время обработки в секундах"
    )

    error_message: Optional[str] = Field(
        default=None, description="Сообщение об ошибке, если операция неуспешна"
    )

    audio_duration: Optional[float] = Field(
        default=None, description="Длительность обработанного аудио в секундах"
    )


class HealthResponse(BaseModel):
    """Модель ответа проверки здоровья сервиса"""

    status: str = Field(description="Статус сервиса")
    model_loaded: bool = Field(description="Загружена ли модель")
    gpu_available: Optional[bool] = Field(default=None, description="Доступен ли GPU")
    memory_usage: Optional[dict] = Field(
        default=None, description="Использование памяти"
    )


class ModelInfo(BaseModel):
    """Информация о загруженной модели"""

    model_name: str = Field(description="Название модели")
    model_path: str = Field(description="Путь к модели")
    sample_rate: int = Field(description="Частота дискретизации модели")
    f0_method: str = Field(description="Метод извлечения f0")
    version: str = Field(description="Версия модели")
