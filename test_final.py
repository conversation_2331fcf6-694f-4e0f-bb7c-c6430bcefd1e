#!/usr/bin/env python3
"""
Финальный тест RVC API с transformers Hubert
"""

import os
import sys
import time

def test_health():
    """Тест health endpoint"""
    cmd = 'curl -s http://127.0.0.1:8000/health'
    result = os.system(cmd)
    return result == 0

def test_conversion():
    """Тест конвертации голоса"""
    if not os.path.exists("1093.mp3"):
        print("❌ Тестовый файл 1093.mp3 не найден!")
        return False
    
    print(f"📁 Размер входного файла: {os.path.getsize('1093.mp3')} байт")
    
    # Удаляем старый результат если есть
    if os.path.exists("converted_final.wav"):
        os.remove("converted_final.wav")
    
    cmd = '''curl -X POST "http://127.0.0.1:8000/convert" \
        -F "audio_file=@1093.mp3" \
        -F "f0_up_key=0" \
        -F "index_rate=0.75" \
        -F "filter_radius=3" \
        -F "rms_mix_rate=0.25" \
        -F "protect=0.33" \
        -F "f0_method=rmvpe" \
        -o converted_final.wav'''
    
    print("🔄 Запуск конвертации...")
    start_time = time.time()
    result = os.system(cmd)
    end_time = time.time()
    
    if result == 0 and os.path.exists("converted_final.wav"):
        output_size = os.path.getsize("converted_final.wav")
        print(f"✅ Конвертация успешна!")
        print(f"📁 Размер выходного файла: {output_size} байт")
        print(f"⏱️  Время обработки: {end_time - start_time:.2f} секунд")
        return True
    else:
        print(f"❌ Конвертация не удалась (код: {result})")
        return False

def test_different_parameters():
    """Тест с разными параметрами"""
    tests = [
        {"name": "Повышение тона (+5)", "f0_up_key": 5, "output": "converted_high.wav"},
        {"name": "Понижение тона (-3)", "f0_up_key": -3, "output": "converted_low.wav"},
        {"name": "Без индекса", "index_rate": 0.0, "output": "converted_no_index.wav"},
    ]
    
    success_count = 0
    for test in tests:
        print(f"\n🧪 Тест: {test['name']}")
        
        # Удаляем старый результат
        if os.path.exists(test['output']):
            os.remove(test['output'])
        
        cmd = f'''curl -X POST "http://127.0.0.1:8000/convert" \
            -F "audio_file=@1093.mp3" \
            -F "f0_up_key={test.get('f0_up_key', 0)}" \
            -F "index_rate={test.get('index_rate', 0.75)}" \
            -F "filter_radius=3" \
            -F "rms_mix_rate=0.25" \
            -F "protect=0.33" \
            -F "f0_method=rmvpe" \
            -o {test['output']}'''
        
        result = os.system(cmd)
        
        if result == 0 and os.path.exists(test['output']):
            size = os.path.getsize(test['output'])
            print(f"   ✅ Успешно! Размер: {size} байт")
            success_count += 1
        else:
            print(f"   ❌ Не удалось")
    
    return success_count == len(tests)

def main():
    """Основная функция тестирования"""
    print("🎤 === Финальный тест RVC Voice Conversion API ===")
    print("🔧 Используется: transformers + HuBERT + Python 3.12\n")
    
    # Тест 1: Проверка сервера
    print("1️⃣  Проверка сервера...")
    if not test_health():
        print("❌ Сервер недоступен! Запустите: uv run main.py")
        return False
    print("✅ Сервер работает\n")
    
    # Тест 2: Базовая конвертация
    print("2️⃣  Базовая конвертация...")
    if not test_conversion():
        print("❌ Базовая конвертация не удалась")
        return False
    print("✅ Базовая конвертация работает\n")
    
    # Тест 3: Разные параметры
    print("3️⃣  Тест различных параметров...")
    if not test_different_parameters():
        print("❌ Некоторые тесты параметров не прошли")
    else:
        print("✅ Все тесты параметров прошли")
    
    print("\n🎉 === ТЕСТИРОВАНИЕ ЗАВЕРШЕНО ===")
    print("📋 Результаты:")
    print("   • Сервер запущен и отвечает")
    print("   • Hubert модель загружена через transformers")
    print("   • RVC модель w_zaporozhec1_ru.pth работает")
    print("   • Конвертация голоса функционирует")
    print("   • API принимает различные параметры")
    print("\n🚀 Сервис готов к использованию!")
    print("📖 Документация API: http://127.0.0.1:8000/docs")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
