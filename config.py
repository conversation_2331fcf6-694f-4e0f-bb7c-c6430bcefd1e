import os
from pathlib import Path
from pydantic_settings import BaseSettings
from typing import Optional
import torch
from multiprocessing import cpu_count

# Корневая директория проекта
ROOT_DIR = Path(__file__).parent


class Settings(BaseSettings):
    """Настройки приложения"""

    # Пути к моделям и файлам (упрощенные пути)
    weights_dir: str = str(ROOT_DIR / "weights")
    pretrained_dir: str = str(ROOT_DIR / "pretrained_v2")
    model_path: str = str(ROOT_DIR / "weights" / "w_zaporozhec1_ru.pth")
    hubert_model_path: str = str(ROOT_DIR / "hubert_base.pt")
    rmvpe_model_path: str = str(ROOT_DIR / "rmvpe.pt")
    index_file_path: str = ""  # Путь к индексному файлу (опционально)

    # Настройки устройства
    device: str = "cuda:0" if torch.cuda.is_available() else "cpu"
    is_half: bool = True if torch.cuda.is_available() else False

    # Настройки обработки аудио (из docs_project)
    input_sample_rate: int = 16000  # Частота для Hubert модели

    # Параметры по умолчанию для конвертации (из Gradio интерфейса)
    default_f0_method: str = "rmvpe"  # Лучший метод по комментариям
    default_f0_up_key: int = 0  # Без изменения тональности
    default_index_rate: float = 0.75  # Коэффициент использования индекса
    default_filter_radius: int = 3  # Радиус медианного фильтра
    default_resample_sr: int = 0  # Без ресемплинга
    default_rms_mix_rate: float = 0.25  # Смешивание RMS огибающей
    default_protect: float = 0.33  # Защита согласных

    # Настройки производительности
    n_cpu: int = 0  # 0 = автоопределение

    # Настройки памяти (будут настроены автоматически)
    x_pad: int = 3
    x_query: int = 10
    x_center: int = 60
    x_max: int = 65

    # Настройки API
    max_file_size: int = 100 * 1024 * 1024  # 100MB
    allowed_audio_formats: list = ["audio/wav", "audio/mpeg", "audio/flac", "audio/ogg"]

    # Настройки логирования
    log_level: str = "INFO"

    class Config:
        env_prefix = "RVC_"
        case_sensitive = False


def get_device_config(
    device: str, is_half: bool, gpu_mem: Optional[int] = None
) -> tuple:
    """
    Получение конфигурации устройства для оптимальной производительности
    Упрощенная версия на основе docs_project/config.py
    """
    if is_half:
        # 6G+ GPU configuration (из docs_project)
        x_pad = 3
        x_query = 10
        x_center = 60
        x_max = 65
    else:
        # 5G GPU or CPU configuration (из docs_project)
        x_pad = 1
        x_query = 6
        x_center = 38
        x_max = 41

    # Для GPU с малым объемом памяти (из docs_project)
    if gpu_mem is not None and gpu_mem <= 4:
        x_pad = 1
        x_query = 5
        x_center = 30
        x_max = 32

    return x_pad, x_query, x_center, x_max


def get_settings() -> Settings:
    """Получение настроек с автоконфигурацией устройства (упрощенная версия)"""
    settings = Settings()

    # Автоконфигурация CPU
    if settings.n_cpu == 0:
        settings.n_cpu = cpu_count()

    # Упрощенная автоконфигурация устройства
    if torch.cuda.is_available():
        try:
            i_device = int(settings.device.split(":")[-1])
            gpu_name = torch.cuda.get_device_name(i_device)
            gpu_mem = int(
                torch.cuda.get_device_properties(i_device).total_memory
                / 1024
                / 1024
                / 1024
                + 0.4
            )

            print(f"Found GPU: {gpu_name}, Memory: {gpu_mem}GB")

            # Проверка на старые GPU (из docs_project)
            if (
                ("16" in gpu_name and "V100" not in gpu_name.upper())
                or "P40" in gpu_name.upper()
                or "1060" in gpu_name
                or "1070" in gpu_name
                or "1080" in gpu_name
            ):
                print(f"GPU {gpu_name} requires fp32, disabling half precision")
                settings.is_half = False

            # Настройка параметров памяти
            x_pad, x_query, x_center, x_max = get_device_config(
                settings.device, settings.is_half, gpu_mem
            )
            settings.x_pad = x_pad
            settings.x_query = x_query
            settings.x_center = x_center
            settings.x_max = x_max

        except Exception as e:
            print(f"Error configuring GPU: {e}, falling back to CPU")
            settings.device = "cpu"
            settings.is_half = False
    else:
        print("No CUDA available, using CPU")
        settings.device = "cpu"
        settings.is_half = False

    # CPU конфигурация
    if settings.device == "cpu":
        x_pad, x_query, x_center, x_max = get_device_config(
            settings.device, settings.is_half
        )
        settings.x_pad = x_pad
        settings.x_query = x_query
        settings.x_center = x_center
        settings.x_max = x_max

    # Проверка существования файлов моделей
    _check_model_files(settings)

    return settings


def _check_model_files(settings: Settings):
    """Проверка существования файлов моделей"""
    if not os.path.exists(settings.model_path):
        print(f"Warning: Model file not found: {settings.model_path}")

    if not os.path.exists(settings.hubert_model_path):
        print(f"Warning: Hubert model file not found: {settings.hubert_model_path}")

    if not os.path.exists(settings.rmvpe_model_path):
        print(f"Warning: RMVPE model file not found: {settings.rmvpe_model_path}")

    # Проверка директорий
    os.makedirs(settings.weights_dir, exist_ok=True)
    os.makedirs(settings.pretrained_dir, exist_ok=True)
