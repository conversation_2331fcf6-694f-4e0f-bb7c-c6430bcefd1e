# Используем официальный Python образ
FROM python:3.10-slim

# Установка системных зависимостей
RUN apt-get update && apt-get install -y \
    ffmpeg \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Создание рабочей директории
WORKDIR /app

# Копирование файлов проекта
COPY requirements.txt .
COPY *.py ./
COPY pyproject.toml .

# Установка Python зависимостей
RUN pip install --no-cache-dir -r requirements.txt

# Создание директорий для моделей
RUN mkdir -p /app/models /app/weights /app/pretrained_v2

# Переменные окружения
ENV PYTHONPATH=/app
ENV RVC_MODEL_PATH=/app/weights/w_zaporozhec1_ru.pth
ENV RVC_HUBERT_MODEL_PATH=/app/hubert_base.pt
ENV RVC_RMVPE_MODEL_PATH=/app/rmvpe.pt

# Порт приложения
EXPOSE 8000

# Команда запуска
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
