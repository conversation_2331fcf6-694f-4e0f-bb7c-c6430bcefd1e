#!/usr/bin/env python3
"""
Простой тест API для проверки работы сервиса конвертации голоса
"""

import requests
import json
import sys
import os


def test_health():
    """Тест health endpoint"""
    try:
        response = requests.get("http://localhost:8000/health")
        print(f"Health check status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Health response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"Health check failed: {response.text}")
            return False
    except Exception as e:
        print(f"Error testing health: {e}")
        return False


def test_root():
    """Тест root endpoint"""
    try:
        response = requests.get("http://localhost:8000/")
        print(f"Root endpoint status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Root response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"Root endpoint failed: {response.text}")
            return False
    except Exception as e:
        print(f"Error testing root: {e}")
        return False


def create_test_audio():
    """Создание тестового аудио файла"""
    try:
        import numpy as np
        import soundfile as sf

        # Создаем простой синусоидальный сигнал
        duration = 2  # секунды
        sample_rate = 16000
        frequency = 440  # Hz (нота A)

        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio = np.sin(2 * np.pi * frequency * t) * 0.5

        test_file = "test_audio.wav"
        sf.write(test_file, audio, sample_rate)
        print(f"Created test audio file: {test_file}")
        return test_file
    except Exception as e:
        print(f"Error creating test audio: {e}")
        return None


def test_convert(audio_file):
    """Тест convert endpoint"""
    if not os.path.exists(audio_file):
        print(f"Audio file not found: {audio_file}")
        return False

    try:
        with open(audio_file, "rb") as f:
            files = {"audio_file": f}
            data = {
                "f0_up_key": 0,
                "index_rate": 0.75,
                "filter_radius": 3,
                "rms_mix_rate": 0.25,
                "protect": 0.33,
                "f0_method": "rmvpe",
            }

            print("Sending conversion request...")
            response = requests.post(
                "http://localhost:8000/convert", files=files, data=data
            )

        print(f"Convert status: {response.status_code}")

        if response.status_code == 200:
            # Сохраняем результат
            output_file = "converted_audio.wav"
            with open(output_file, "wb") as f:
                f.write(response.content)
            print(f"Conversion successful! Output saved to: {output_file}")

            # Проверяем заголовки
            if "X-Processing-Time" in response.headers:
                print(
                    f"Processing time: {response.headers['X-Processing-Time']} seconds"
                )

            return True
        else:
            print(f"Conversion failed: {response.text}")
            return False

    except Exception as e:
        print(f"Error testing convert: {e}")
        return False


def main():
    """Основная функция тестирования"""
    print("=== RVC Voice Conversion API Test ===\n")

    # Тест 1: Health check
    print("1. Testing health endpoint...")
    if not test_health():
        print("Health check failed, exiting")
        return False
    print()

    # Тест 2: Root endpoint
    print("2. Testing root endpoint...")
    if not test_root():
        print("Root endpoint failed")
    print()

    # Тест 3: Создание тестового аудио
    print("3. Creating test audio...")
    test_audio = create_test_audio()
    if not test_audio:
        print("Failed to create test audio, exiting")
        return False
    print()

    # Тест 4: Конвертация
    print("4. Testing voice conversion...")
    if test_convert(test_audio):
        print("Voice conversion test passed!")
    else:
        print("Voice conversion test failed!")
        return False

    print("\n=== All tests completed ===")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
