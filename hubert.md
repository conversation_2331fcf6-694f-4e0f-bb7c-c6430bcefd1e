Загрузка моделей HuBERT с помощью `fairseq` в Python 3.12 сопряжена с трудностями, поскольку `fairseq` официально не поддерживает эту версию Python и могут возникать проблемы с совместимостью.

Современный и рекомендуемый способ загрузки моделей HuBERT, особенно в новых версиях Python — это использование библиотеки `transformers` от Hugging Face. Она стала стандартом для работы с моделями-трансформерами и активно поддерживается.

Вот подробная инструкция, как загружать модели HuBERT в Python 3.12.

### Переход с `fairseq` на `transformers` от Hugging Face

Библиотека `transformers` предоставляет простой интерфейс для загрузки и использования тысяч предварительно обученных моделей, включая HuBERT.

**Шаг 1: Установите необходимые библиотеки**

Прежде всего, вам нужно установить `transformers` и `torch` (PyTorch). Убедитесь, что у вас также установлена библиотека `datasets` для удобной работы с аудиоданными и `soundfile` для их чтения.

```bash
uv add transformers torch datasets soundfile
```

**Ша-г 2: Загрузка модели HuBERT и процессора**

Для работы с аудио, HuBERT использует специальный процессор (часто `Wav2Vec2FeatureExtractor`) для преобразования необработанных аудиосигналов в формат, понятный модели.

Вот пример кода для загрузки базовой модели HuBERT, дообученной для распознавания речи на датасете LibriSpeech.

```python
import torch
from transformers import HubertForCTC, Wav2Vec2Processor
from datasets import load_dataset
import soundfile as sf

# 1. Загрузка модели и процессора
model_id = "facebook/hubert-large-ls960-ft"
model = HubertForCTC.from_pretrained(model_id)
processor = Wav2Vec2Processor.from_pretrained(model_id)

# 2. Подготовка аудиоданных
# Загрузим пример аудиофайла из датасета
ds = load_dataset("patrickvonplaten/librispeech_asr_dummy", "clean", split="validation")
audio_path = ds[0]["file"]

def load_audio(path):
    """Загружает аудиофайл и преобразует его в нужный формат."""
    speech, sample_rate = sf.read(path)
    # Убедитесь, что частота дискретизации аудио 16 кГц, как и у модели
    # Если нет, его нужно будет передискретизировать
    if sample_rate != 16000:
        raise ValueError("Аудиофайл должен иметь частоту дискретизации 16 кГц.")
    return speech

speech_array = load_audio(audio_path)

# 3. Обработка аудио и получение предсказаний
# Преобразуем аудио в тензор
inputs = processor(speech_array, sampling_rate=16000, return_tensors="pt")

# Передаем данные в модель
with torch.no_grad():
    logits = model(**inputs).logits

# 4. Декодирование результатов
predicted_ids = torch.argmax(logits, dim=-1)
transcription = processor.batch_decode(predicted_ids)

print("Распознанный текст:", transcription[0])
```

### Ключевые моменты и пояснения:

*   **`HubertForCTC`**: Это класс модели HuBERT с головой для задачи Connectionist Temporal Classification (CTC), которая обычно используется для распознавания речи.
*   **`Wav2Vec2Processor`**: Хотя модель называется HuBERT, для обработки аудио она часто использует процессор от Wav2Vec2. Этот процессор объединяет в себе `feature_extractor` для преобразования аудио в числовой формат и `tokenizer` для преобразования вывода модели обратно в текст.
*   **Идентификатор модели (`model_id`)**: Hugging Face предоставляет целый хаб моделей. "facebook/hubert-large-ls960-ft" — это одна из популярных моделей HuBERT. Вы можете найти другие модели HuBERT на [сайте Hugging Face](https://huggingface.co/models?search=hubert).
*   **Частота дискретизации**: Модели HuBERT, как правило, обучены на аудио с частотой дискретизации 16 кГц. Важно, чтобы ваши входные аудиоданные имели ту же частоту.

### Если вам нужна только базовая модель (без головы для конкретной задачи)

Если вам нужны только скрытые состояния (эмбеддинги) из модели HuBERT, а не решение конкретной задачи вроде распознавания речи, вы можете загрузить базовую модель.

```python
import torch
from transformers import HubertModel, Wav2Vec2FeatureExtractor

# Загрузка модели и экстрактора признаков
model_id = "facebook/hubert-base-ls960"
model = HubertModel.from_pretrained(model_id)
feature_extractor = Wav2Vec2FeatureExtractor.from_pretrained(model_id)

# ... (загрузка аудио такая же, как в примере выше) ...
speech_array = load_audio(audio_path)

# Обработка аудио
inputs = feature_extractor(speech_array, sampling_rate=16000, return_tensors="pt")

# Получение скрытых состояний
with torch.no_grad():
    hidden_states = model(**inputs).last_hidden_state

print("Размер выходного тензора:", hidden_states.shape)
```

Этот подход дает вам векторные представления аудио, которые затем можно использовать для различных задач, таких как классификация аудио, распознавание эмоций и т.д.